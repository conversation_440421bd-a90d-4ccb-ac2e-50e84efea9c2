# 更新日志

## [v0.0.2] - 2024-01-XX

### 🚀 新增功能

#### OpenAI 流式输出支持
- **流式API集成**：支持OpenAI流式输出，实时生成关键词数据
- **JSON修复功能**：使用`jsonrepair`库自动修复AI返回的不完整JSON数据
- **按行更新机制**：每收到完整的一行数据就尝试解析和更新词云
- **智能去重**：避免重复更新相同的词云数据，减少不必要的渲染
- **错误恢复**：即使中间解析失败，也会继续尝试，直到获得有效数据

#### 用户体验优化
- **实时响应**：AI生成过程中实时更新词云，无需等待完整响应
- **减少抖动**：按行更新策略显著减少界面抖动
- **流畅交互**：词云更新更加平滑自然

### 🔧 技术改进

#### 新增文件
- `examples/streamingExample.ts` - 流式输出使用示例
- `tests/streaming.test.ts` - 流式输出功能测试
- `CHANGELOG.md` - 更新日志文件

#### 更新文件
- `lib/openaiService.ts`
  - 新增 `generateTextStreamWithLineProcessing()` 方法
  - 新增 `tryParseAndRepairJSON()` 方法
  - 导入 `jsonrepair` 库
- `lib/wordcloudProcessor.ts`
  - 支持进度回调函数 `onProgress`
  - 集成流式API调用
  - 新增 `parseOpenAIResponseWithRepair()` 函数
  - 新增 `enhanceKeywordsWithPosts()` 函数
- `components/wordcloud/WordCloudPanel.tsx`
  - 集成流式更新回调
  - 支持实时词云数据更新
- `package.json`
  - 新增测试脚本：`test:streaming`、`test:openai`、`test:basic`
- `README.md`
  - 更新技术栈说明
  - 新增流式输出特性介绍
  - 更新开发说明

### 📦 依赖更新
- 已使用现有的 `jsonrepair@^3.13.0` 依赖
- 无需额外安装新依赖

### 🧪 测试覆盖
- JSON修复功能测试
- 流式文本生成测试
- 词云数据处理测试
- 集成测试模拟

### 📚 文档更新
- 新增流式输出使用示例
- 更新README中的功能介绍
- 新增开发说明中的流式输出特性

---

## [v0.0.1] - 2024-01-XX

### 🎉 初始版本

#### 核心功能
- **词云显示**：基于Canvas的词云渲染
- **帖子检测**：使用结构聚类算法检测网页帖子列表
- **OpenAI集成**：基础的OpenAI API调用支持
- **交互功能**：悬停和点击关键词查看详情

#### 技术栈
- Plasmo框架 + React + TypeScript
- Tailwind CSS + Shadcn UI
- OpenAI API集成
- 原生Canvas渲染

#### 基础文件结构
- 词云组件：`components/wordcloud/`
- 帖子检测：`lib/postDetection.ts`
- OpenAI服务：`lib/openaiService.ts`
- 数据处理：`lib/wordcloudUtils.ts`
- 内容脚本：`contents/WordCloudContent.tsx`

---

## 版本说明

- **主版本号**：重大功能更新或架构变更
- **次版本号**：新功能添加
- **修订版本号**：Bug修复和小改进

## 贡献指南

如果您想为项目贡献代码，请：

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 问题反馈

如果您发现任何问题或有功能建议，请在 GitHub Issues 中提交。