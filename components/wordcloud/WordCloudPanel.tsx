import React, { useState, useEffect, useRef } from 'react'
import { ChevronDown, ChevronUp, Tag, AlertCircle, Settings, RefreshCw } from 'lucide-react'
import WordCloudComponent from './WordCloud'
import DetailPanel from './DetailPanel'
import { processKeywordsForWordCloud } from '../../lib/wordcloudUtils'
import { detectPostLists, highlightDetectedPosts } from '../../lib/postDetection'
import { processPagePosts } from '../../lib/wordcloudProcessor'

interface WordCloudItem {
  text: string
  size: number
  relatedPosts?: PostInfo[]
}

interface PostInfo {
  id: number
  title: string
  link?: string
  author?: string
  time?: string
  xpath?: string
}

const WordCloudPanel: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [wordCloudData, setWordCloudData] = useState<WordCloudItem[]>([])
  const [loading, setLoading] = useState(true)
  const [hoveredPosts, setHoveredPosts] = useState<PostInfo[] | null>(null)
  const [hoveredWord, setHoveredWord] = useState<string | null>(null)
  const [currentViewingWord, setCurrentViewingWord] = useState<string | null>(null)
  const [currentViewingPosts, setCurrentViewingPosts] = useState<PostInfo[] | null>(null)
  const [debugHighlight, setDebugHighlight] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)



  // 面板悬停状态
  const [isPanelHovered, setIsPanelHovered] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  // 数据获取状态
  const [hasDataFetched, setHasDataFetched] = useState(false)

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [])



  // 点击外部缩小面板
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      
      // 检查点击是否在面板外部
      // 由于Plasmo框架的特殊结构，需要检查多个可能的容器
      const panelElement = target.closest('[data-wordcloud-panel]') || 
                          target.closest('plasmo-csui') ||
                          (target.tagName === 'PLASMO-CSUI')
      
      if (!panelElement && isExpanded) {
        setIsExpanded(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isExpanded])

  // 拖动事件处理
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return
      
      const deltaY = e.clientY - dragStart.y
      setPosition(prev => ({
        x: prev.x, // 保持x位置不变，只允许垂直拖动
        y: prev.y + deltaY
      }))
      setDragStart({ x: dragStart.x, y: e.clientY })
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, dragStart])

  // 拖动开始处理
  const handleDragMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation() // 阻止事件冒泡到标题栏的onClick
    setIsDragging(true)
    setDragStart({ x: e.clientX, y: e.clientY })
  }

  // 处理网页文章数据并更新词云
  const handleProcessPagePosts = async (forceRefresh = false) => {
    try {
      setLoading(true)
      setApiError(null)

      // 调用API处理文章列表，传入进度回调函数
      const keywordsData = await processPagePosts((progressKeywords) => {
        // 实时更新词云数据
        processKeywordsForWordCloud(progressKeywords).then(cloudData => {
          setWordCloudData(cloudData)
          // 一旦有流式数据，就停止显示加载状态
          if (cloudData.length > 0) {
            setLoading(false)
          }
        }).catch(error => {
          console.warn('[词云面板] 流式更新失败:', error)
        })
      })

      if (keywordsData && keywordsData.length > 0) {
        // 转换为词云数据格式（最终数据）
        const cloudData = await processKeywordsForWordCloud(keywordsData)

        // 更新词云数据
        setWordCloudData(cloudData)
        setHasDataFetched(true)
      } else {
        setWordCloudData([])
        setHasDataFetched(true)
      }
    } catch (error: any) {
      console.error('[词云面板] 处理网页数据失败:', error)

      // 检查是否是API配置错误
      if (error.message && (
          error.message.includes('未配置OpenAI API密钥') ||
          error.message.includes('OpenAI 服务未初始化') ||
          error.message.includes('API key')
        )) {
        setApiError('需要配置OpenAI API密钥。请点击扩展图标，进入选项页配置。')
      } else {
        setApiError(`处理失败: ${error.message || '未知错误'}`)
      }

      // 清空词云数据
      setWordCloudData([])
      setHasDataFetched(true)
    } finally {
      // 确保加载状态最终被清除
      setLoading(false)
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    setHasDataFetched(false)
    handleProcessPagePosts(true)
  }

  const handleWordHover = (word: string | null, posts?: PostInfo[], event?: MouseEvent) => {
    // 如果有固定查看的词，不响应悬停事件
    if (currentViewingWord) {
      return
    }

    // 清除之前的隐藏定时器
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }

    if (word && posts) {
      // 显示帖子列表
      setHoveredWord(word)
      setHoveredPosts(posts)
    } else {
      // 延迟隐藏帖子列表
      hideTimeoutRef.current = setTimeout(() => {
        setHoveredWord(null)
        setHoveredPosts(null)
      }, 150) // 150ms延迟
    }
  }

  const handleWordClick = (word: string, posts?: PostInfo[]) => {
    if (currentViewingWord === word) {
      // 如果点击的是当前查看的词，取消固定显示
      setCurrentViewingWord(null)
      setCurrentViewingPosts(null)
    } else {
      // 清除悬停定时器
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
        hideTimeoutRef.current = null
      }
      
      // 先设置新的固定查看词，然后清除悬停状态
      // 这样确保DetailPanel始终有数据显示
      setCurrentViewingWord(word)
      setCurrentViewingPosts(posts || [])
      
      // 延迟清除悬停状态，确保新的固定状态已经生效
      setTimeout(() => {
        setHoveredWord(null)
        setHoveredPosts(null)
      }, 0)
    }
  }

  const handleDetailPanelClose = () => {
    // 关闭详情面板
    setCurrentViewingWord(null)
    setCurrentViewingPosts(null)
  }

  // 处理帖子点击事件
  const handlePostClick = (post: PostInfo) => {
    // 如果帖子有链接，则在新标签页中打开
    if (post.link) {
      window.open(post.link, '_blank')
    }
  }

  const handlePanelToggle = () => {
    const newExpandedState = !isExpanded
    setIsExpanded(newExpandedState)

    // 如果是展开操作，则触发帖子列表检测
    if (newExpandedState) {

      // 如果已经有数据，就不重新获取
      if (hasDataFetched) {
        return
      }

      // 延迟执行检测，确保面板动画完成
      setTimeout(async () => {
        try {
          const clusters = detectPostLists()

          if (clusters.length > 0) {
            // 可选：高亮显示检测到的帖子（用于调试）
            if (debugHighlight) {
              highlightDetectedPosts(clusters)
            }

            // 处理网页文章数据并更新词云
            await handleProcessPagePosts()
          } else {
            setLoading(false)
            setWordCloudData([])
            setHasDataFetched(true)
          }
        } catch (error) {
          console.error('[词云面板] 帖子检测过程中出现错误:', error)
          setLoading(false)
          setWordCloudData([])
        }
      }, 300) // 300ms延迟，等待展开动画完成
    }
  }

  // 打开选项页面
  const openOptionsPage = () => {
    chrome.runtime.openOptionsPage();
  };

  return (
    <div
      className="fixed right-4 z-[10000] transition-all duration-300 ease-in-out [visibility:visible!important]"
      style={{
        top: `${16 + position.y}px`,
        cursor: isDragging ? 'grabbing' : 'default'
      }}
      data-wordcloud-panel
      onMouseEnter={() => setIsPanelHovered(true)}
      onMouseLeave={() => setIsPanelHovered(false)}
    >
      <div className="flex gap-3">
        {/* 详情面板 */}
        {isExpanded && (currentViewingWord || hoveredWord) && (
          <DetailPanel
            hoveredWord={currentViewingWord || hoveredWord}
            hoveredPosts={currentViewingPosts || hoveredPosts}
            onPostClick={handlePostClick}
            onClose={currentViewingWord ? handleDetailPanelClose : undefined}
            isFixed={!!currentViewingWord}
          />
        )}

        {/* 词云面板 */}
        <div
          className="bg-white border border-gray-200 rounded-lg shadow-lg transition-all duration-300 ease-in-out"
          style={{
            width: isExpanded ? '450px' : '200px',
            maxHeight: isExpanded ? '500px' : '50px',
            overflow: 'hidden',
            opacity: isPanelHovered || isExpanded ? 1 : 0.7,
            cursor: 'default'
          }}
        >
          {/* 标题栏 */}
          <div 
            className="flex items-center justify-between p-3 hover:bg-gray-50 transition-colors cursor-pointer"
            onClick={handlePanelToggle}
          >
            <div className="flex items-center gap-2">
              <Tag 
                 className="w-4 h-4 text-blue-500 cursor-grab active:cursor-grabbing" 
                 onMouseDown={handleDragMouseDown}
                 onClick={(e) => e.stopPropagation()} // 阻止点击事件冒泡到标题栏
               />
              <span
                className="text-sm font-medium text-gray-700 select-none"
                onDoubleClick={(e) => {
                  e.stopPropagation()
                  setDebugHighlight(!debugHighlight)
                }}
                title={debugHighlight ? "双击关闭调试高亮" : "双击启用调试高亮"}
              >
                关键词词云{debugHighlight && ' 🔍'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/* 刷新按钮 */}
              {isExpanded && (
                <button
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRefresh()
                  }}
                  title="刷新词云数据"
                  disabled={loading}
                >
                  <RefreshCw className={`w-4 h-4 text-gray-500 ${loading ? 'animate-spin' : ''}`} />
                </button>
              )}
              {isExpanded ? (
                <ChevronUp className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              )}
            </div>
          </div>

          {/* 词云内容 */}
          {isExpanded && (
            <div className="p-3 pt-0">
              {/* API错误提示 */}
              {apiError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md mb-3 flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <div className="text-xs flex-1">{apiError}</div>
                  <button 
                    className="flex items-center gap-1 bg-red-100 hover:bg-red-200 rounded px-2 py-1 text-xs transition-colors"
                    onClick={openOptionsPage}
                    title="打开设置页面"
                  >
                    <Settings className="w-3 h-3" />
                    设置
                  </button>
                </div>
              )}
              
              {wordCloudData.length > 0 ? (
                <div className="space-y-3">
                  <WordCloudComponent
                    words={wordCloudData}
                    width={400}
                    height={300}
                    minFontSize={15}
                    maxFontSize={60}
                    onWordHover={handleWordHover}
                    onWordClick={handleWordClick}
                  />

                  <div className="text-xs text-gray-500 text-center">
                    共 {wordCloudData.length} 个热门关键词
                    {loading && (
                      <span className="ml-2 text-blue-600">• 实时更新中...</span>
                    )}
                    {hoveredWord && (
                      <span className="ml-2 text-blue-600">• 悬停: {hoveredWord}</span>
                    )}
                  </div>
                </div>
              ) : loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-sm text-gray-500">加载中...</div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="text-sm text-gray-500">暂无数据</div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default WordCloudPanel